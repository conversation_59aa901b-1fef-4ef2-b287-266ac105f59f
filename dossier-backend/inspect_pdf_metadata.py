#!/usr/bin/env python3
"""
PDF Metadata Inspector

A comprehensive tool to inspect both standard PDF metadata (Option A with prefixed keywords)
and XMP metadata in PDF files. Useful for verifying the PDF metadata fix.

Usage:
    python3 inspect_pdf_metadata_fixed.py path/to/document.pdf
    python3 inspect_pdf_metadata_fixed.py --format json path/to/document.pdf
    python3 inspect_pdf_metadata_fixed.py --verbose path/to/document.pdf
"""

import json
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Dict, Any

import click
import PyPDF2


class PDFMetadataInspector:
    """Inspector for PDF metadata with support for both standard and XMP metadata."""

    def __init__(self, pdf_path: str):
        self.pdf_path = Path(pdf_path)
        self.file_handle = None
        self.reader = None
        self._load_pdf()

    def _load_pdf(self):
        """Load the PDF file and keep file handle open."""
        try:
            self.file_handle = open(self.pdf_path, "rb")
            self.reader = PyPDF2.PdfReader(self.file_handle)
        except Exception as e:
            if self.file_handle:
                self.file_handle.close()
            raise click.ClickException(f"Failed to load PDF: {e}")

    def close(self):
        """Close the file handle."""
        if self.file_handle:
            self.file_handle.close()
            self.file_handle = None

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def get_standard_metadata(self) -> Dict[str, Any]:
        """Extract standard PDF metadata (Option A format)."""
        metadata = {}

        if not self.reader.metadata:
            return metadata

        # Extract basic metadata
        for key, value in self.reader.metadata.items():
            clean_key = key.lstrip("/")
            metadata[clean_key] = str(value) if value else None

        # Parse keywords if present
        keywords_raw = metadata.get("Keywords", "")
        if keywords_raw:
            metadata["parsed_keywords"] = self._parse_keywords(keywords_raw)

        return metadata

    def _parse_keywords(self, keywords_str: str) -> Dict[str, Any]:
        """Parse prefixed keywords from Option A format."""
        if not keywords_str:
            return {}

        keywords = [k.strip() for k in keywords_str.split(",") if k.strip()]
        structured = {}
        searchable = []

        for keyword in keywords:
            if ":" in keyword:
                prefix, value = keyword.split(":", 1)
                structured[prefix.strip()] = value.strip()
            else:
                searchable.append(keyword)

        return {"structured": structured, "searchable": searchable, "raw": keywords_str}

    def get_xmp_metadata(self) -> Dict[str, Any]:
        """Extract XMP metadata."""
        xmp_data = {}

        # Try to get XMP metadata from the catalog
        try:
            if hasattr(self.reader, "trailer") and "/Root" in self.reader.trailer:
                root = self.reader.trailer["/Root"]
                if "/Metadata" in root:
                    metadata_obj = root["/Metadata"]
                    if hasattr(metadata_obj, "get_data"):
                        xmp_raw = metadata_obj.get_data().decode("utf-8")
                        xmp_data = self._parse_xmp(xmp_raw)
                        xmp_data["_raw_available"] = True
                    else:
                        xmp_data["_raw_available"] = False
                else:
                    xmp_data["_raw_available"] = False
            else:
                xmp_data["_raw_available"] = False
        except Exception as e:
            xmp_data["_error"] = str(e)
            xmp_data["_raw_available"] = False

        return xmp_data

    def _parse_xmp(self, xmp_raw: str) -> Dict[str, Any]:
        """Parse XMP XML data."""
        try:
            # Find the XMP content between xmpmeta tags - handle different namespace prefixes
            import re
            xmpmeta_pattern = r'<[^:]*:xmpmeta[^>]*>'
            xmpmeta_end_pattern = r'</[^:]*:xmpmeta>'

            start_match = re.search(xmpmeta_pattern, xmp_raw)
            end_match = re.search(xmpmeta_end_pattern, xmp_raw)

            if not start_match or not end_match:
                return {"_parse_error": "XMP markers not found"}

            start_idx = start_match.start()
            end_idx = end_match.end()
            xml_content = xmp_raw[start_idx:end_idx]
            root = ET.fromstring(xml_content)

            # Define namespaces
            namespaces = {
                "x": "adobe:ns:meta/",
                "rdf": "http://www.w3.org/1999/02/22-rdf-syntax-ns#",
                "dc": "http://purl.org/dc/elements/1.1/",
                "pdf": "http://ns.adobe.com/pdf/1.3/",
                "hypodossier": "http://hypodossier.com/ns/1.0/",
            }

            # Find the description element
            description = root.find(".//rdf:Description", namespaces)
            if description is None:
                return {"_parse_error": "No RDF Description found"}

            metadata = {}

            # Extract Dublin Core fields
            dc_fields = ["title", "creator", "description"]
            for field in dc_fields:
                elem = description.find(f"dc:{field}", namespaces)
                if elem is not None and elem.text:
                    metadata[f"dc_{field}"] = elem.text

            # Extract subject array
            subject_elem = description.find("dc:subject", namespaces)
            if subject_elem is not None:
                bag = subject_elem.find("rdf:Bag", namespaces)
                if bag is not None:
                    subjects = [
                        li.text for li in bag.findall("rdf:li", namespaces) if li.text
                    ]
                    if subjects:
                        metadata["dc_subject"] = subjects

            # Extract PDF fields
            pdf_fields = ["Keywords", "Producer"]
            for field in pdf_fields:
                elem = description.find(f"pdf:{field}", namespaces)
                if elem is not None and elem.text:
                    metadata[f"pdf_{field.lower()}"] = elem.text

            # Extract HypoDossier custom fields
            hypodossier_fields = [
                "DocumentCategoryKey",
                "TitleSuffix",
                "Version",
                "UUID",
            ]
            for field in hypodossier_fields:
                elem = description.find(f"hypodossier:{field}", namespaces)
                if elem is not None and elem.text:
                    metadata[f"hypodossier_{field.lower()}"] = elem.text

            return metadata

        except ET.ParseError as e:
            return {"_parse_error": f"XML parsing failed: {e}"}
        except Exception as e:
            return {"_parse_error": f"Unexpected error: {e}"}

    def get_pdf_info(self) -> Dict[str, Any]:
        """Get basic PDF information."""
        info = {
            "file_path": str(self.pdf_path),
            "file_size": self.pdf_path.stat().st_size,
            "num_pages": len(self.reader.pages) if self.reader.pages else 0,
        }

        # Check if PDF is encrypted
        info["is_encrypted"] = self.reader.is_encrypted

        return info

    def inspect_all(self) -> Dict[str, Any]:
        """Perform complete inspection of the PDF."""
        return {
            "pdf_info": self.get_pdf_info(),
            "standard_metadata": self.get_standard_metadata(),
            "xmp_metadata": self.get_xmp_metadata(),
        }


def print_formatted_output(data: Dict[str, Any], verbose: bool = False):
    """Print formatted inspection results."""
    click.echo(click.style("=== PDF METADATA INSPECTION ===", fg="blue", bold=True))

    # PDF Info
    pdf_info = data["pdf_info"]
    click.echo(f"\n📄 File: {pdf_info['file_path']}")
    click.echo(f"📊 Size: {pdf_info['file_size']:,} bytes")
    click.echo(f"📑 Pages: {pdf_info['num_pages']}")
    if pdf_info["is_encrypted"]:
        click.echo(click.style("🔒 Encrypted: Yes", fg="yellow"))

    # Standard Metadata
    click.echo(
        click.style("\n1. STANDARD PDF METADATA (Option A)", fg="green", bold=True)
    )
    click.echo("─" * 50)

    std_metadata = data["standard_metadata"]
    if not std_metadata:
        click.echo(click.style("❌ No standard metadata found", fg="red"))
    else:
        # Basic fields
        basic_fields = ["Title", "Author", "Subject", "Creator", "Producer"]
        for field in basic_fields:
            value = std_metadata.get(field)
            if value:
                click.echo(f"📝 {field}: {value}")

        # Keywords
        if "Keywords" in std_metadata:
            click.echo(f"🏷️  Keywords (raw): {std_metadata['Keywords']}")

            if "parsed_keywords" in std_metadata:
                parsed = std_metadata["parsed_keywords"]
                if parsed["structured"]:
                    click.echo("   📋 Structured metadata:")
                    for key, val in parsed["structured"].items():
                        click.echo(f"      {key}: {val}")

                if parsed["searchable"]:
                    click.echo(
                        f"   🔍 Searchable terms: {', '.join(parsed['searchable'])}"
                    )

        if verbose:
            click.echo("\n   📋 All standard fields:")
            for key, value in std_metadata.items():
                if key not in basic_fields + ["Keywords", "parsed_keywords"]:
                    click.echo(f"      {key}: {value}")

    # XMP Metadata
    click.echo(click.style("\n2. XMP METADATA", fg="green", bold=True))
    click.echo("─" * 50)

    xmp_metadata = data["xmp_metadata"]
    if not xmp_metadata.get("_raw_available", False):
        click.echo(click.style("❌ No XMP metadata found", fg="red"))
        if "_error" in xmp_metadata:
            click.echo(f"   Error: {xmp_metadata['_error']}")
    else:
        if "_parse_error" in xmp_metadata:
            click.echo(
                click.style(
                    f"⚠️  XMP parsing error: {xmp_metadata['_parse_error']}", fg="yellow"
                )
            )
        else:
            # Dublin Core
            dc_fields = [k for k in xmp_metadata.keys() if k.startswith("dc_")]
            if dc_fields:
                click.echo("📚 Dublin Core:")
                for field in dc_fields:
                    value = xmp_metadata[field]
                    display_name = field.replace("dc_", "").title()
                    if isinstance(value, list):
                        click.echo(f"   {display_name}: {', '.join(value)}")
                    else:
                        click.echo(f"   {display_name}: {value}")

            # PDF namespace
            pdf_fields = [k for k in xmp_metadata.keys() if k.startswith("pdf_")]
            if pdf_fields:
                click.echo("📄 PDF namespace:")
                for field in pdf_fields:
                    value = xmp_metadata[field]
                    display_name = field.replace("pdf_", "").title()
                    click.echo(f"   {display_name}: {value}")

            # HypoDossier custom
            hd_fields = [k for k in xmp_metadata.keys() if k.startswith("hypodossier_")]
            if hd_fields:
                click.echo("🏢 HypoDossier namespace:")
                for field in hd_fields:
                    value = xmp_metadata[field]
                    display_name = (
                        field.replace("hypodossier_", "").replace("_", " ").title()
                    )
                    click.echo(f"   {display_name}: {value}")

            if not dc_fields and not pdf_fields and not hd_fields:
                click.echo(click.style("❌ No recognized XMP fields found", fg="red"))


@click.command()
@click.argument("pdf_path", type=click.Path(exists=True, dir_okay=False, readable=True))
@click.option(
    "--format",
    "output_format",
    type=click.Choice(["text", "json"]),
    default="text",
    help="Output format (default: text)",
)
@click.option(
    "--verbose",
    "-v",
    is_flag=True,
    help="Show verbose output including all metadata fields",
)
@click.option(
    "--output", "-o", type=click.File("w"), help="Output file (default: stdout)"
)
def inspect_pdf(pdf_path: str, output_format: str, verbose: bool, output):
    """
    Inspect PDF metadata including both standard PDF info and XMP metadata.

    This tool is designed to verify the PDF metadata fix that handles both
    SemanticDocumentFullApiData schema objects and SemanticDocument model instances.

    PDF_PATH: Path to the PDF file to inspect
    """
    try:
        with PDFMetadataInspector(pdf_path) as inspector:
            data = inspector.inspect_all()

            if output_format == "json":
                json_output = json.dumps(data, indent=2, ensure_ascii=False)
                if output:
                    output.write(json_output)
                else:
                    click.echo(json_output)
            else:
                # Redirect output if specified
                if output:
                    # For text format with file output, we need to capture the output
                    import io
                    import contextlib

                    f = io.StringIO()
                    with contextlib.redirect_stdout(f):
                        print_formatted_output(data, verbose)
                    output.write(f.getvalue())
                else:
                    print_formatted_output(data, verbose)

    except Exception as e:
        raise click.ClickException(f"Inspection failed: {e}")


if __name__ == "__main__":
    inspect_pdf()
